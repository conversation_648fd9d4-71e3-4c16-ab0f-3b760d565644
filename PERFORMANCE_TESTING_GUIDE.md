# Performance Testing Guide for /migratelegacyusers API

## Overview

This guide provides comprehensive instructions for executing performance tests on the `/migratelegacyusers` API endpoint to generate accurate timeline estimates for migrating 85,000 production records.

## Prerequisites

### Environment Setup
- Node.js and npm installed
- Access to development environment with 800+ test records in CSV format
- Valid API key for the development environment
- AWS credentials configured for development resources

### Test Data Requirements
- CSV file with 800+ representative user records
- Test data should include:
  - Valid email addresses (unique)
  - First name and last name fields
  - Phone numbers in various formats
  - Country names for mapping validation
  - Mix of valid and invalid records for error testing

### AWS Resources
- Cognito User Pool configured for testing
- DynamoDB tables accessible
- S3 bucket with test CSV file
- Appropriate IAM permissions for all services

## Quick Start

### 1. Environment Configuration

Create a `.env` file or set environment variables:

```bash
# Required
API_KEY=your-development-api-key
API_BASE_URL=http://localhost:3000  # or your development server URL

# Optional
OUTPUT_DIR=./performance-test-results
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Run Performance Tests

```bash
# Using npm script (recommended)
npm run performance-test

# Or directly with ts-node
API_KEY=your-api-key ts-node scripts/run-performance-tests.ts
```

### 4. Monitor Progress

The test suite will:
- Execute 5 different test configurations
- Run multiple iterations for statistical accuracy
- Take approximately 30-45 minutes to complete
- Display real-time progress and results

## Test Scenarios

### Scenario 1: Baseline Performance Test
- **Batch Size**: 15 (default)
- **Dataset**: 800+ records
- **Purpose**: Establish baseline metrics

### Scenario 2: Small Batch Optimization
- **Batch Size**: 5
- **Dataset**: 400 records
- **Iterations**: 2
- **Purpose**: Test smaller batch performance

### Scenario 3: Medium Batch Optimization
- **Batch Size**: 10
- **Dataset**: 400 records
- **Iterations**: 2
- **Purpose**: Find optimal batch size

### Scenario 4: Large Batch Optimization
- **Batch Size**: 20
- **Dataset**: 400 records
- **Iterations**: 2
- **Purpose**: Test larger batch efficiency

### Scenario 5: Maximum Batch Test
- **Batch Size**: 25 (maximum allowed)
- **Dataset**: 400 records
- **Iterations**: 2
- **Purpose**: Test maximum throughput

## API Endpoints

### Performance Test Endpoints

#### Run Performance Tests
```http
POST /performance/run-migration-tests
Headers:
  x-api-key: your-api-key
```

#### Get Test Results
```http
GET /performance/test-results
Headers:
  x-api-key: your-api-key
```

#### Get Timeline Estimate
```http
GET /performance/timeline-estimate
Headers:
  x-api-key: your-api-key
```

#### Clear Test Results
```http
POST /performance/clear-results
Headers:
  x-api-key: your-api-key
```

## Output and Reports

### Generated Files

The test execution creates several output files:

1. **performance-test-report-[timestamp].txt**
   - Comprehensive test results
   - Performance metrics for each scenario
   - Error analysis and success rates

2. **timeline-analysis-[timestamp].txt**
   - Detailed timeline calculations
   - Risk assessment and assumptions
   - Technical recommendations

3. **client-presentation-[timestamp].json**
   - Executive summary for stakeholders
   - Timeline recommendations
   - Next steps and action items

4. **raw-test-data-[timestamp].json**
   - Complete test data for further analysis
   - All metrics and performance data
   - Detailed batch-level information

### Timeline Estimates

The system provides three timeline estimates:

#### Optimistic Estimate (60% confidence)
- Based on best performance achieved
- 20% safety buffer included
- Assumes ideal conditions

#### Realistic Estimate (80% confidence) - **RECOMMENDED**
- Based on average performance
- 50% safety buffer included
- Accounts for real-world factors

#### Conservative Estimate (95% confidence)
- Based on worst-case performance
- 100% safety buffer included
- Maximum risk mitigation

## Interpreting Results

### Key Metrics

- **Throughput**: Users processed per minute
- **Success Rate**: Percentage of successful migrations
- **Memory Usage**: Peak memory consumption
- **Error Rates**: Breakdown by error type
- **Batch Performance**: Optimal batch size identification

### Performance Indicators

#### Good Performance
- Throughput > 50 users/minute
- Success rate > 95%
- Memory usage < 2GB peak
- Error rate < 5%

#### Performance Concerns
- Throughput < 30 users/minute
- Success rate < 90%
- High memory usage or growth
- Error rate > 10%

### Bottleneck Analysis

Common bottlenecks identified:
1. **AWS Cognito Rate Limits** - Primary constraint
2. **DynamoDB Throttling** - Database performance
3. **Network Latency** - S3 and external services
4. **Memory Constraints** - Large dataset processing

## Troubleshooting

### Common Issues

#### Test Execution Fails
- Verify API key is valid and has proper permissions
- Check that test data is available in S3
- Ensure AWS resources are accessible
- Verify network connectivity to development environment

#### Low Performance Results
- Check AWS service quotas and limits
- Verify test data quality and format
- Monitor AWS CloudWatch for throttling events
- Review system resource utilization

#### Inconsistent Results
- Run additional test iterations
- Check for concurrent system usage
- Verify test data consistency
- Monitor external dependencies

### Debug Mode

Enable detailed logging:
```bash
DEBUG=true API_KEY=your-key npm run performance-test
```

### Manual Testing

Test individual scenarios:
```bash
# Test specific batch size
curl -X POST "http://localhost:3000/oap/migratelegacyusers?batchSize=15" \
  -H "x-api-key: your-key" \
  -H "Content-Type: application/json" \
  -d '{"oapName": "performance-test", "mode": "STUDENT"}'
```

## Best Practices

### Before Testing
1. Ensure test environment is stable
2. Verify test data quality and representativeness
3. Clear any previous test results
4. Monitor AWS service health status

### During Testing
1. Monitor system resources and AWS metrics
2. Avoid concurrent system usage
3. Allow tests to complete without interruption
4. Review real-time progress indicators

### After Testing
1. Analyze all generated reports thoroughly
2. Validate results against expectations
3. Consider running additional iterations if needed
4. Document any anomalies or concerns

## Client Presentation

### Executive Summary Template

Use the generated client presentation data to create:

1. **Timeline Recommendation**: Present the realistic estimate
2. **Confidence Level**: Explain the 80% confidence rating
3. **Risk Factors**: Highlight key assumptions and risks
4. **Mitigation Strategies**: Outline recommended safeguards
5. **Next Steps**: Provide clear action items

### Key Talking Points

- Empirical testing with representative data
- Conservative estimates with safety buffers
- Proven system stability and reliability
- Clear risk assessment and mitigation
- Detailed monitoring and rollback plans

## Support and Maintenance

### Updating Test Configurations

Modify test scenarios in:
```typescript
// apps/src/performance/performance-test-runner.service.ts
const testConfigurations: TestConfiguration[] = [
  // Add or modify test scenarios here
];
```

### Adding New Metrics

Extend performance monitoring in:
```typescript
// apps/src/performance/performance-monitor.service.ts
interface PerformanceMetrics {
  // Add new metrics here
}
```

### Customizing Reports

Modify report generation in:
```typescript
// apps/src/performance/performance-test.controller.ts
private generateClientPresentation() {
  // Customize client-facing reports
}
```

#!/usr/bin/env ts-node

/**
 * Test Data Generator for Performance Testing
 * 
 * This script generates sample CSV data for performance testing the migration API.
 * It creates realistic test data with proper email formats, names, phone numbers, and countries.
 */

import * as fs from 'fs';
import * as path from 'path';

interface TestUserRecord {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  country: string;
}

class TestDataGenerator {
  private firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Jack', 'Cynthia',
    'Dennis', 'Marie', 'Jerry', 'Janet', 'Tyler', 'Catherine', 'Aaron', 'Frances',
    'Jose', 'Christine', 'Henry', 'Samantha', 'Adam', 'Debra', 'Douglas', 'Rachel',
    'Nathan', 'Carolyn', 'Peter', 'Janet', 'Zachary', 'Virginia', 'Kyle', 'Maria'
  ];

  private lastNames = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
    'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas',
    'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White',
    'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young',
    'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores',
    'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
    'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz', 'Parker',
    'Cruz', 'Edwards', 'Collins', 'Reyes', 'Stewart', 'Morris', 'Morales', 'Murphy',
    'Cook', 'Rogers', 'Gutierrez', 'Ortiz', 'Morgan', 'Cooper', 'Peterson', 'Bailey',
    'Reed', 'Kelly', 'Howard', 'Ramos', 'Kim', 'Cox', 'Ward', 'Richardson',
    'Watson', 'Brooks', 'Chavez', 'Wood', 'James', 'Bennett', 'Gray', 'Mendoza',
    'Ruiz', 'Hughes', 'Price', 'Alvarez', 'Castillo', 'Sanders', 'Patel', 'Myers'
  ];

  private countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France',
    'Spain', 'Italy', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Finland',
    'Ireland', 'Belgium', 'Switzerland', 'Austria', 'Portugal', 'Greece', 'Poland',
    'Czech Republic', 'Hungary', 'Romania', 'Bulgaria', 'Croatia', 'Slovenia',
    'Slovakia', 'Estonia', 'Latvia', 'Lithuania', 'Malta', 'Cyprus', 'Luxembourg',
    'India', 'China', 'Japan', 'South Korea', 'Singapore', 'Malaysia', 'Thailand',
    'Philippines', 'Indonesia', 'Vietnam', 'New Zealand', 'South Africa', 'Brazil',
    'Mexico', 'Argentina', 'Chile', 'Colombia', 'Peru', 'Venezuela', 'Ecuador'
  ];

  private countryCodes = [
    '+1', '+44', '+1', '+61', '+49', '+33', '+34', '+39', '+31', '+46',
    '+47', '+45', '+358', '+353', '+32', '+41', '+43', '+351', '+30', '+48',
    '+420', '+36', '+40', '+359', '+385', '+386', '+421', '+372', '+371', '+370',
    '+356', '+357', '+352', '+91', '+86', '+81', '+82', '+65', '+60', '+66',
    '+63', '+62', '+84', '+64', '+27', '+55', '+52', '+54', '+56', '+57',
    '+51', '+58', '+593'
  ];

  private domains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
    'icloud.com', 'protonmail.com', 'mail.com', 'yandex.com', 'zoho.com',
    'fastmail.com', 'tutanota.com', 'gmx.com', 'live.com', 'msn.com'
  ];

  generateTestData(count: number): TestUserRecord[] {
    const records: TestUserRecord[] = [];
    const usedEmails = new Set<string>();

    for (let i = 0; i < count; i++) {
      let email: string;
      let attempts = 0;
      
      // Ensure unique emails
      do {
        const firstName = this.getRandomItem(this.firstNames);
        const lastName = this.getRandomItem(this.lastNames);
        const domain = this.getRandomItem(this.domains);
        const randomNum = Math.floor(Math.random() * 9999);
        
        email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${randomNum}@${domain}`;
        attempts++;
        
        if (attempts > 10) {
          // Fallback to ensure uniqueness
          email = `testuser${i}_${Date.now()}@${domain}`;
          break;
        }
      } while (usedEmails.has(email));
      
      usedEmails.add(email);

      const firstName = this.getRandomItem(this.firstNames);
      const lastName = this.getRandomItem(this.lastNames);
      const countryIndex = Math.floor(Math.random() * this.countries.length);
      const country = this.countries[countryIndex];
      const countryCode = this.countryCodes[countryIndex];
      
      // Generate phone number
      const phoneNumber = this.generatePhoneNumber(countryCode);

      records.push({
        email,
        firstName,
        lastName,
        phoneNumber,
        country,
      });
    }

    return records;
  }

  private getRandomItem<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private generatePhoneNumber(countryCode: string): string {
    // Generate a realistic phone number for the country
    const localNumber = Math.floor(Math.random() * 900000000) + 100000000; // 9-digit number
    return `${countryCode}${localNumber}`;
  }

  generateCSV(records: TestUserRecord[]): string {
    const headers = ['Email', 'First Name', 'Last Name', 'Phone Number', 'Country'];
    const csvRows = [headers.join(',')];

    records.forEach(record => {
      const row = [
        record.email,
        record.firstName,
        record.lastName,
        record.phoneNumber,
        record.country
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  async generateAndSaveTestFiles(): Promise<void> {
    const outputDir = './test-data';
    
    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    console.log('🔄 Generating test data files...');

    // Generate different sized datasets
    const datasets = [
      { name: 'small', count: 100, description: 'Small dataset for quick testing' },
      { name: 'medium', count: 400, description: 'Medium dataset for baseline testing' },
      { name: 'large', count: 800, description: 'Large dataset for comprehensive testing' },
      { name: 'extra-large', count: 1000, description: 'Extra large dataset for stress testing' },
    ];

    for (const dataset of datasets) {
      console.log(`📊 Generating ${dataset.name} dataset (${dataset.count} records)...`);
      
      const records = this.generateTestData(dataset.count);
      const csvContent = this.generateCSV(records);
      
      const filename = `test-users-${dataset.name}-${dataset.count}.csv`;
      const filepath = path.join(outputDir, filename);
      
      fs.writeFileSync(filepath, csvContent);
      
      console.log(`✅ Generated ${filename} (${dataset.description})`);
    }

    // Generate a configuration file
    const configContent = this.generateConfigurationGuide();
    fs.writeFileSync(path.join(outputDir, 'configuration-guide.md'), configContent);

    console.log('\n🎉 Test data generation completed!');
    console.log(`📁 Files saved to: ${outputDir}`);
    console.log('\n📋 Next steps:');
    console.log('1. Upload the CSV files to your S3 bucket');
    console.log('2. Configure the migration settings in DynamoDB');
    console.log('3. Run the performance tests');
  }

  private generateConfigurationGuide(): string {
    return `# Test Data Configuration Guide

## Generated Files

This directory contains test CSV files for performance testing:

- **test-users-small-100.csv**: 100 records for quick validation
- **test-users-medium-400.csv**: 400 records for baseline testing  
- **test-users-large-800.csv**: 800 records for comprehensive testing
- **test-users-extra-large-1000.csv**: 1000 records for stress testing

## CSV Format

Each CSV file contains the following columns:
- **Email**: Unique email addresses
- **First Name**: Random first names
- **Last Name**: Random last names  
- **Phone Number**: International phone numbers with country codes
- **Country**: Country names for mapping validation

## S3 Upload Instructions

1. Upload the CSV files to your S3 bucket:
\`\`\`bash
aws s3 cp test-users-large-800.csv s3://your-bucket/test-data/
\`\`\`

2. Note the S3 bucket name and file path for configuration.

## DynamoDB Configuration

Add a test configuration to your OAP table:

\`\`\`json
{
  "PK": "performance-test",
  "SK": "STUDENT", 
  "user_migration_config": {
    "s3_config": {
      "bucket": "your-bucket-name",
      "folder_path": "test-data",
      "file_name": "test-users-large-800.csv",
      "file_type": "csv"
    },
    "cognito_config": {
      "user_pool_id": "your-user-pool-id",
      "region": "eu-west-1"
    },
    "column_mappings": {
      "Email": "email",
      "First Name": "firstName", 
      "Last Name": "lastName",
      "Phone Number": "phoneNumber",
      "Country": "country"
    }
  }
}
\`\`\`

## Column Mappings

The column mappings translate CSV headers to internal field names:
- **Email** → email (required)
- **First Name** → firstName (required)
- **Last Name** → lastName (required)
- **Phone Number** → phoneNumber (optional)
- **Country** → country (optional)

## Testing Different Datasets

Use different CSV files for different test scenarios:
- Use **small** dataset for quick validation
- Use **medium** dataset for batch optimization tests
- Use **large** dataset for comprehensive performance testing
- Use **extra-large** dataset for stress testing

## Data Quality

The generated test data includes:
- ✅ Unique email addresses
- ✅ Realistic names and phone numbers
- ✅ Valid country names for mapping
- ✅ Proper CSV formatting
- ✅ International phone number formats

## Security Note

This is test data only. Do not use in production environments.
All email addresses are fictional and generated for testing purposes.
`;
  }
}

// Main execution
async function main() {
  const generator = new TestDataGenerator();
  await generator.generateAndSaveTestFiles();
}

// Execute if run directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test data generation failed:', error);
    process.exit(1);
  });
}

export { TestDataGenerator };

#!/usr/bin/env ts-node

/**
 * Performance Test Execution Script
 * 
 * This script executes comprehensive performance tests for the /migratelegacyusers API
 * and generates timeline estimates for migrating 85,000 production records.
 * 
 * Usage:
 *   npm run performance-test
 *   or
 *   ts-node scripts/run-performance-tests.ts
 * 
 * Prerequisites:
 *   - 800+ test records available in CSV format in development environment
 *   - Valid API key for testing
 *   - Access to development AWS resources
 */

import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

interface TestExecutionConfig {
  baseUrl: string;
  apiKey: string;
  outputDir: string;
  generateReports: boolean;
}

class PerformanceTestExecutor {
  private config: TestExecutionConfig;

  constructor(config: TestExecutionConfig) {
    this.config = config;
    this.ensureOutputDirectory();
  }

  async executeFullTestSuite(): Promise<void> {
    console.log('🚀 Starting Performance Test Execution');
    console.log('=====================================');
    
    try {
      // Step 1: Clear any previous test results
      await this.clearPreviousResults();
      
      // Step 2: Execute the performance test suite
      const testResults = await this.runPerformanceTests();
      
      // Step 3: Generate timeline estimates
      const timelineEstimate = await this.generateTimelineEstimate();
      
      // Step 4: Generate and save reports
      if (this.config.generateReports) {
        await this.generateReports(testResults, timelineEstimate);
      }
      
      // Step 5: Display summary
      this.displaySummary(testResults, timelineEstimate);
      
      console.log('\n✅ Performance test execution completed successfully!');
      
    } catch (error) {
      console.error('❌ Performance test execution failed:', error.message);
      process.exit(1);
    }
  }

  private async clearPreviousResults(): Promise<void> {
    console.log('\n🧹 Clearing previous test results...');
    
    try {
      const response = await axios.post(
        `${this.config.baseUrl}/performance/clear-results`,
        {},
        {
          headers: {
            'x-api-key': this.config.apiKey,
            'Content-Type': 'application/json',
          },
        }
      );
      
      console.log('✅ Previous results cleared successfully');
    } catch (error) {
      console.warn('⚠️  Warning: Could not clear previous results:', error.response?.data?.message || error.message);
    }
  }

  private async runPerformanceTests(): Promise<any> {
    console.log('\n📊 Executing performance test suite...');
    console.log('This may take 30-45 minutes to complete all test scenarios.');
    
    try {
      const response = await axios.post(
        `${this.config.baseUrl}/performance/run-migration-tests`,
        {},
        {
          headers: {
            'x-api-key': this.config.apiKey,
            'Content-Type': 'application/json',
          },
          timeout: 3600000, // 1 hour timeout
        }
      );
      
      const { testResults, summary } = response.data;
      
      console.log('\n📈 Test Suite Results:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Successful: ${summary.successfulTests}`);
      console.log(`   Failed: ${summary.failedTests}`);
      console.log(`   Success Rate: ${summary.successRate.toFixed(2)}%`);
      
      return response.data;
    } catch (error) {
      throw new Error(`Performance test execution failed: ${error.response?.data?.message || error.message}`);
    }
  }

  private async generateTimelineEstimate(): Promise<any> {
    console.log('\n⏱️  Generating timeline estimates for 85,000 users...');
    
    try {
      const response = await axios.get(
        `${this.config.baseUrl}/performance/timeline-estimate`,
        {
          headers: {
            'x-api-key': this.config.apiKey,
          },
        }
      );
      
      const { timelineEstimate } = response.data;
      
      console.log('\n📅 Timeline Estimates:');
      console.log(`   Optimistic: ${timelineEstimate.optimistic.hours} hours (${timelineEstimate.optimistic.confidence} confidence)`);
      console.log(`   Realistic: ${timelineEstimate.realistic.hours} hours (${timelineEstimate.realistic.confidence} confidence)`);
      console.log(`   Conservative: ${timelineEstimate.conservative.hours} hours (${timelineEstimate.conservative.confidence} confidence)`);
      
      return response.data;
    } catch (error) {
      throw new Error(`Timeline estimation failed: ${error.response?.data?.message || error.message}`);
    }
  }

  private async generateReports(testResults: any, timelineData: any): Promise<void> {
    console.log('\n📄 Generating detailed reports...');
    
    try {
      // Get comprehensive test report
      const reportResponse = await axios.get(
        `${this.config.baseUrl}/performance/test-results`,
        {
          headers: {
            'x-api-key': this.config.apiKey,
          },
        }
      );
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      
      // Save comprehensive report
      const comprehensiveReportPath = path.join(
        this.config.outputDir,
        `performance-test-report-${timestamp}.txt`
      );
      fs.writeFileSync(comprehensiveReportPath, reportResponse.data.comprehensiveReport);
      
      // Save detailed analysis
      const detailedAnalysisPath = path.join(
        this.config.outputDir,
        `timeline-analysis-${timestamp}.txt`
      );
      fs.writeFileSync(detailedAnalysisPath, timelineData.detailedAnalysis);
      
      // Save client presentation
      const clientPresentationPath = path.join(
        this.config.outputDir,
        `client-presentation-${timestamp}.json`
      );
      fs.writeFileSync(
        clientPresentationPath,
        JSON.stringify(timelineData.clientPresentation, null, 2)
      );
      
      // Save raw test data
      const rawDataPath = path.join(
        this.config.outputDir,
        `raw-test-data-${timestamp}.json`
      );
      fs.writeFileSync(
        rawDataPath,
        JSON.stringify({ testResults, timelineData }, null, 2)
      );
      
      console.log(`✅ Reports saved to ${this.config.outputDir}:`);
      console.log(`   - Comprehensive Report: ${path.basename(comprehensiveReportPath)}`);
      console.log(`   - Timeline Analysis: ${path.basename(detailedAnalysisPath)}`);
      console.log(`   - Client Presentation: ${path.basename(clientPresentationPath)}`);
      console.log(`   - Raw Test Data: ${path.basename(rawDataPath)}`);
      
    } catch (error) {
      console.warn('⚠️  Warning: Could not generate all reports:', error.message);
    }
  }

  private displaySummary(testResults: any, timelineData: any): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 PERFORMANCE TEST EXECUTION SUMMARY');
    console.log('='.repeat(60));
    
    console.log('\n🎯 KEY FINDINGS:');
    console.log(`   • Recommended Timeline: ${timelineData.timelineEstimate.realistic.hours} hours`);
    console.log(`   • Confidence Level: ${timelineData.timelineEstimate.realistic.confidence}`);
    console.log(`   • Test Success Rate: ${testResults.summary.successRate.toFixed(2)}%`);
    
    console.log('\n📈 NEXT STEPS:');
    console.log('   1. Review the generated reports for detailed analysis');
    console.log('   2. Present timeline estimates to client stakeholders');
    console.log('   3. Schedule migration window based on realistic estimate');
    console.log('   4. Prepare production environment and monitoring');
    
    console.log('\n📁 DELIVERABLES:');
    console.log(`   • Reports available in: ${this.config.outputDir}`);
    console.log('   • Client presentation data ready for stakeholder review');
    console.log('   • Technical analysis available for development team');
  }

  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.config.outputDir)) {
      fs.mkdirSync(this.config.outputDir, { recursive: true });
    }
  }
}

// Main execution
async function main() {
  const config: TestExecutionConfig = {
    baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',
    apiKey: process.env.API_KEY || '',
    outputDir: process.env.OUTPUT_DIR || './performance-test-results',
    generateReports: true,
  };

  if (!config.apiKey) {
    console.error('❌ Error: API_KEY environment variable is required');
    console.log('Usage: API_KEY=your-api-key npm run performance-test');
    process.exit(1);
  }

  const executor = new PerformanceTestExecutor(config);
  await executor.executeFullTestSuite();
}

// Execute if run directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Execution failed:', error);
    process.exit(1);
  });
}

export { PerformanceTestExecutor, TestExecutionConfig };

import {
  <PERSON>,
  Post,
  Get,
  Headers,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PerformanceTestRunnerService, TestResult, TimelineEstimate } from './performance-test-runner.service';
import { PerformanceMonitorService } from './performance-monitor.service';

@Controller('performance')
export class PerformanceTestController {
  constructor(
    private readonly performanceTestRunner: PerformanceTestRunnerService,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}

  @Post('/run-migration-tests')
  async runPerformanceTests(
    @Headers('x-api-key') apiKey: string,
  ): Promise<{
    message: string;
    testResults: TestResult[];
    summary: {
      totalTests: number;
      successfulTests: number;
      failedTests: number;
      successRate: number;
    };
  }> {
    try {
      if (!apiKey) {
        throw new BadRequestException('API key is required');
      }

      console.log('🚀 Starting comprehensive performance test suite...');
      
      // Clear any previous results
      this.performanceTestRunner.clearResults();
      
      // Run the full test suite
      const testResults = await this.performanceTestRunner.runPerformanceTestSuite(apiKey);
      
      const successfulTests = testResults.filter(r => r.success);
      const failedTests = testResults.filter(r => !r.success);
      
      const summary = {
        totalTests: testResults.length,
        successfulTests: successfulTests.length,
        failedTests: failedTests.length,
        successRate: (successfulTests.length / testResults.length) * 100,
      };

      console.log('✅ Performance test suite completed successfully!');
      console.log(`📊 Results: ${summary.successfulTests}/${summary.totalTests} tests passed (${summary.successRate.toFixed(2)}%)`);

      return {
        message: 'Performance test suite completed successfully',
        testResults,
        summary,
      };
    } catch (error) {
      console.error('❌ Performance test suite failed:', error);
      throw new InternalServerErrorException(
        `Performance test suite failed: ${error.message}`,
      );
    }
  }

  @Get('/test-results')
  async getTestResults(): Promise<{
    testResults: TestResult[];
    comprehensiveReport: string;
    summary: {
      totalTests: number;
      successfulTests: number;
      failedTests: number;
      successRate: number;
    };
  }> {
    try {
      const testResults = this.performanceTestRunner.getTestResults();
      
      if (testResults.length === 0) {
        return {
          testResults: [],
          comprehensiveReport: 'No test results available. Please run the performance test suite first.',
          summary: {
            totalTests: 0,
            successfulTests: 0,
            failedTests: 0,
            successRate: 0,
          },
        };
      }

      const comprehensiveReport = this.performanceTestRunner.generateComprehensiveReport();
      const successfulTests = testResults.filter(r => r.success);
      const failedTests = testResults.filter(r => !r.success);
      
      const summary = {
        totalTests: testResults.length,
        successfulTests: successfulTests.length,
        failedTests: failedTests.length,
        successRate: (successfulTests.length / testResults.length) * 100,
      };

      return {
        testResults,
        comprehensiveReport,
        summary,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to retrieve test results: ${error.message}`,
      );
    }
  }

  @Get('/timeline-estimate')
  async getTimelineEstimate(): Promise<{
    timelineEstimate: TimelineEstimate;
    detailedAnalysis: string;
    clientPresentation: {
      executiveSummary: string;
      recommendations: string[];
      nextSteps: string[];
    };
  }> {
    try {
      const testResults = this.performanceTestRunner.getTestResults();
      
      if (testResults.filter(r => r.success).length === 0) {
        throw new BadRequestException(
          'No successful test results available. Please run the performance test suite first.',
        );
      }

      const timelineEstimate = this.performanceTestRunner.calculateTimelineFor85000Users();
      
      const detailedAnalysis = this.generateDetailedAnalysis(timelineEstimate, testResults);
      const clientPresentation = this.generateClientPresentation(timelineEstimate, testResults);

      return {
        timelineEstimate,
        detailedAnalysis,
        clientPresentation,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to calculate timeline estimate: ${error.message}`,
      );
    }
  }

  @Post('/clear-results')
  async clearTestResults(): Promise<{ message: string }> {
    try {
      this.performanceTestRunner.clearResults();
      this.performanceMonitor.reset();
      
      return {
        message: 'All test results and performance data cleared successfully',
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to clear test results: ${error.message}`,
      );
    }
  }

  @Get('/current-metrics')
  async getCurrentMetrics(): Promise<{
    currentMetrics: any;
    isActive: boolean;
    report: string;
  }> {
    try {
      const metrics = this.performanceMonitor.getMetrics();
      const report = this.performanceMonitor.generateReport();
      const isActive = metrics.totalStartTime > 0 && metrics.totalProcessingTime === 0;

      return {
        currentMetrics: metrics,
        isActive,
        report,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to retrieve current metrics: ${error.message}`,
      );
    }
  }

  private generateDetailedAnalysis(estimate: TimelineEstimate, testResults: TestResult[]): string {
    const successfulTests = testResults.filter(r => r.success);
    const bestTest = successfulTests.reduce((best, current) => 
      current.metrics.throughputMetrics.usersPerMinute > best.metrics.throughputMetrics.usersPerMinute 
        ? current : best
    );

    return `
=== DETAILED TIMELINE ANALYSIS FOR 85,000 USER MIGRATION ===

PERFORMANCE TEST SUMMARY:
- Total Tests Conducted: ${testResults.length}
- Successful Tests: ${successfulTests.length}
- Best Throughput Achieved: ${bestTest.metrics.throughputMetrics.usersPerMinute.toFixed(2)} users/minute
- Optimal Batch Size: ${bestTest.configuration.batchSize}

TIMELINE ESTIMATES:
🟢 OPTIMISTIC (${estimate.optimistic.confidence} confidence): ${estimate.optimistic.hours} hours
   ${estimate.optimistic.description}

🟡 REALISTIC (${estimate.realistic.confidence} confidence): ${estimate.realistic.hours} hours
   ${estimate.realistic.description}

🔴 CONSERVATIVE (${estimate.conservative.confidence} confidence): ${estimate.conservative.hours} hours
   ${estimate.conservative.description}

KEY ASSUMPTIONS:
${estimate.assumptions.map(a => `• ${a}`).join('\n')}

IDENTIFIED RISKS:
${estimate.risks.map(r => `• ${r}`).join('\n')}

PERFORMANCE BOTTLENECKS IDENTIFIED:
• Cognito API rate limits (primary constraint)
• DynamoDB batch write operations
• Country code lookup operations
• Network I/O for S3 and external services

OPTIMIZATION RECOMMENDATIONS:
• Use batch size of ${bestTest.configuration.batchSize} for optimal throughput
• Implement adaptive rate limiting based on AWS service responses
• Consider parallel processing streams if infrastructure allows
• Pre-cache country code mappings to reduce lookup overhead
• Monitor AWS service quotas and request increases if needed
`;
  }

  private generateClientPresentation(estimate: TimelineEstimate, testResults: TestResult[]): {
    executiveSummary: string;
    recommendations: string[];
    nextSteps: string[];
  } {
    const successfulTests = testResults.filter(r => r.success);
    const avgThroughput = successfulTests.reduce((sum, t) => sum + t.metrics.throughputMetrics.usersPerMinute, 0) / successfulTests.length;

    return {
      executiveSummary: `
Based on comprehensive performance testing with ${testResults.length} test scenarios using 800+ representative records, we have established empirical baselines for the legacy user migration process.

KEY FINDINGS:
• Average processing throughput: ${avgThroughput.toFixed(0)} users per minute
• Recommended timeline for 85,000 users: ${estimate.realistic.hours} hours (${estimate.realistic.confidence} confidence)
• System demonstrates stable performance with ${((successfulTests.length / testResults.length) * 100).toFixed(0)}% success rate
• Primary bottleneck: AWS Cognito user creation API rate limits

TIMELINE RECOMMENDATION:
We recommend planning for ${estimate.realistic.hours} hours of processing time, which provides a realistic estimate with appropriate safety buffers for production deployment.
      `,
      recommendations: [
        `Schedule migration during low-traffic periods to minimize system impact`,
        `Implement monitoring and alerting for the migration process`,
        `Prepare rollback procedures in case of critical failures`,
        `Consider breaking the migration into smaller chunks over multiple days`,
        `Ensure AWS service quotas are adequate for the planned throughput`,
        `Have technical support available during the migration window`,
        `Validate a small subset of production data before full migration`,
      ],
      nextSteps: [
        `Review and approve the ${estimate.realistic.hours}-hour timeline estimate`,
        `Schedule the migration window with appropriate stakeholders`,
        `Prepare production environment and validate configurations`,
        `Conduct final validation with a small production data sample`,
        `Execute the full migration with real-time monitoring`,
        `Perform post-migration validation and reporting`,
      ],
    };
  }
}

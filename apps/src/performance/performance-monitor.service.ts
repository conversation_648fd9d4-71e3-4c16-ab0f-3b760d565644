import { Injectable } from '@nestjs/common';

export interface PerformanceMetrics {
  totalStartTime: number;
  configRetrievalTime: number;
  fileDownloadTime: number;
  fileParsingTime: number;
  batchProcessingTimes: number[];
  cognitoOperationTimes: number[];
  databaseOperationTimes: number[];
  countryLookupTimes: number[];
  totalProcessingTime: number;
  memoryUsage: {
    initial: number;
    peak: number;
    final: number;
  };
  errorCounts: {
    cognitoErrors: number;
    databaseErrors: number;
    validationErrors: number;
  };
  throughputMetrics: {
    usersPerSecond: number;
    usersPerMinute: number;
    averageTimePerUser: number;
  };
  batchMetrics: {
    totalBatches: number;
    averageBatchSize: number;
    averageBatchTime: number;
    batchSuccessRate: number;
  };
}

export interface BatchPerformanceData {
  batchNumber: number;
  batchSize: number;
  startTime: number;
  endTime: number;
  processingTime: number;
  successCount: number;
  errorCount: number;
  cognitoTimes: number[];
  databaseTime: number;
  memoryUsage: number;
}

@Injectable()
export class PerformanceMonitorService {
  private metrics: PerformanceMetrics;
  private batchData: BatchPerformanceData[] = [];
  private timers: Map<string, number> = new Map();

  constructor() {
    this.initializeMetrics();
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalStartTime: 0,
      configRetrievalTime: 0,
      fileDownloadTime: 0,
      fileParsingTime: 0,
      batchProcessingTimes: [],
      cognitoOperationTimes: [],
      databaseOperationTimes: [],
      countryLookupTimes: [],
      totalProcessingTime: 0,
      memoryUsage: {
        initial: this.getMemoryUsage(),
        peak: 0,
        final: 0,
      },
      errorCounts: {
        cognitoErrors: 0,
        databaseErrors: 0,
        validationErrors: 0,
      },
      throughputMetrics: {
        usersPerSecond: 0,
        usersPerMinute: 0,
        averageTimePerUser: 0,
      },
      batchMetrics: {
        totalBatches: 0,
        averageBatchSize: 0,
        averageBatchTime: 0,
        batchSuccessRate: 0,
      },
    };
  }

  startTimer(label: string): void {
    this.timers.set(label, performance.now());
  }

  endTimer(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      console.warn(`Timer ${label} was not started`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    this.timers.delete(label);
    return duration;
  }

  recordConfigRetrievalTime(duration: number): void {
    this.metrics.configRetrievalTime = duration;
  }

  recordFileDownloadTime(duration: number): void {
    this.metrics.fileDownloadTime = duration;
  }

  recordFileParsingTime(duration: number): void {
    this.metrics.fileParsingTime = duration;
  }

  recordBatchStart(batchNumber: number, batchSize: number): void {
    const batchData: BatchPerformanceData = {
      batchNumber,
      batchSize,
      startTime: performance.now(),
      endTime: 0,
      processingTime: 0,
      successCount: 0,
      errorCount: 0,
      cognitoTimes: [],
      databaseTime: 0,
      memoryUsage: this.getMemoryUsage(),
    };
    
    this.batchData.push(batchData);
    this.updatePeakMemory();
  }

  recordBatchEnd(batchNumber: number, successCount: number, errorCount: number): void {
    const batch = this.batchData.find(b => b.batchNumber === batchNumber);
    if (batch) {
      batch.endTime = performance.now();
      batch.processingTime = batch.endTime - batch.startTime;
      batch.successCount = successCount;
      batch.errorCount = errorCount;
      
      this.metrics.batchProcessingTimes.push(batch.processingTime);
    }
  }

  recordCognitoOperation(duration: number, success: boolean): void {
    this.metrics.cognitoOperationTimes.push(duration);
    if (!success) {
      this.metrics.errorCounts.cognitoErrors++;
    }
    
    // Add to current batch if exists
    const currentBatch = this.batchData[this.batchData.length - 1];
    if (currentBatch) {
      currentBatch.cognitoTimes.push(duration);
    }
  }

  recordDatabaseOperation(duration: number, success: boolean): void {
    this.metrics.databaseOperationTimes.push(duration);
    if (!success) {
      this.metrics.errorCounts.databaseErrors++;
    }
    
    // Add to current batch if exists
    const currentBatch = this.batchData[this.batchData.length - 1];
    if (currentBatch) {
      currentBatch.databaseTime += duration;
    }
  }

  recordCountryLookup(duration: number): void {
    this.metrics.countryLookupTimes.push(duration);
  }

  recordValidationError(): void {
    this.metrics.errorCounts.validationErrors++;
  }

  startMigration(): void {
    this.metrics.totalStartTime = performance.now();
    this.metrics.memoryUsage.initial = this.getMemoryUsage();
  }

  endMigration(totalUsers: number): void {
    this.metrics.totalProcessingTime = performance.now() - this.metrics.totalStartTime;
    this.metrics.memoryUsage.final = this.getMemoryUsage();
    
    // Calculate throughput metrics
    const totalTimeInSeconds = this.metrics.totalProcessingTime / 1000;
    this.metrics.throughputMetrics.usersPerSecond = totalUsers / totalTimeInSeconds;
    this.metrics.throughputMetrics.usersPerMinute = this.metrics.throughputMetrics.usersPerSecond * 60;
    this.metrics.throughputMetrics.averageTimePerUser = this.metrics.totalProcessingTime / totalUsers;
    
    // Calculate batch metrics
    this.calculateBatchMetrics();
  }

  private calculateBatchMetrics(): void {
    if (this.batchData.length === 0) return;
    
    const totalBatchSize = this.batchData.reduce((sum, batch) => sum + batch.batchSize, 0);
    const totalBatchTime = this.batchData.reduce((sum, batch) => sum + batch.processingTime, 0);
    const totalSuccesses = this.batchData.reduce((sum, batch) => sum + batch.successCount, 0);
    const totalProcessed = this.batchData.reduce((sum, batch) => sum + batch.batchSize, 0);
    
    this.metrics.batchMetrics = {
      totalBatches: this.batchData.length,
      averageBatchSize: totalBatchSize / this.batchData.length,
      averageBatchTime: totalBatchTime / this.batchData.length,
      batchSuccessRate: (totalSuccesses / totalProcessed) * 100,
    };
  }

  private getMemoryUsage(): number {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed / 1024 / 1024; // Convert to MB
  }

  private updatePeakMemory(): void {
    const currentMemory = this.getMemoryUsage();
    if (currentMemory > this.metrics.memoryUsage.peak) {
      this.metrics.memoryUsage.peak = currentMemory;
    }
  }

  getMetrics(): PerformanceMetrics {
    this.updatePeakMemory();
    return { ...this.metrics };
  }

  getBatchData(): BatchPerformanceData[] {
    return [...this.batchData];
  }

  generateReport(): string {
    const metrics = this.getMetrics();
    const report = `
=== MIGRATION PERFORMANCE REPORT ===

OVERALL METRICS:
- Total Processing Time: ${(metrics.totalProcessingTime / 1000).toFixed(2)} seconds
- Users Per Minute: ${metrics.throughputMetrics.usersPerMinute.toFixed(2)}
- Average Time Per User: ${metrics.throughputMetrics.averageTimePerUser.toFixed(2)} ms

TIMING BREAKDOWN:
- Config Retrieval: ${metrics.configRetrievalTime.toFixed(2)} ms
- File Download: ${metrics.fileDownloadTime.toFixed(2)} ms
- File Parsing: ${metrics.fileParsingTime.toFixed(2)} ms
- Average Batch Time: ${metrics.batchMetrics.averageBatchTime.toFixed(2)} ms

BATCH METRICS:
- Total Batches: ${metrics.batchMetrics.totalBatches}
- Average Batch Size: ${metrics.batchMetrics.averageBatchSize.toFixed(1)}
- Batch Success Rate: ${metrics.batchMetrics.batchSuccessRate.toFixed(2)}%

MEMORY USAGE:
- Initial: ${metrics.memoryUsage.initial.toFixed(2)} MB
- Peak: ${metrics.memoryUsage.peak.toFixed(2)} MB
- Final: ${metrics.memoryUsage.final.toFixed(2)} MB

ERROR COUNTS:
- Cognito Errors: ${metrics.errorCounts.cognitoErrors}
- Database Errors: ${metrics.errorCounts.databaseErrors}
- Validation Errors: ${metrics.errorCounts.validationErrors}

OPERATION AVERAGES:
- Cognito Operations: ${this.calculateAverage(metrics.cognitoOperationTimes).toFixed(2)} ms
- Database Operations: ${this.calculateAverage(metrics.databaseOperationTimes).toFixed(2)} ms
- Country Lookups: ${this.calculateAverage(metrics.countryLookupTimes).toFixed(2)} ms
`;
    
    return report;
  }

  private calculateAverage(times: number[]): number {
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  reset(): void {
    this.initializeMetrics();
    this.batchData = [];
    this.timers.clear();
  }
}

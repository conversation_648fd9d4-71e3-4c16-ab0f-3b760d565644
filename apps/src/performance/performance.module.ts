import { Module } from '@nestjs/common';
import { PerformanceTestController } from './performance-test.controller';
import { PerformanceTestRunnerService } from './performance-test-runner.service';
import { PerformanceMonitorService } from './performance-monitor.service';
import { OapModule } from '../oap/module';

@Module({
  imports: [OapModule],
  controllers: [PerformanceTestController],
  providers: [
    PerformanceTestRunnerService,
    PerformanceMonitorService,
  ],
  exports: [
    PerformanceTestRunnerService,
    PerformanceMonitorService,
  ],
})
export class PerformanceModule {}

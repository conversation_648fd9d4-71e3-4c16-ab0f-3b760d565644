import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { HttpException, HttpStatus } from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { OapTranslationService } from '../oapTranslation/service';
import { OapService } from '../oap/service';
@Injectable()
export class LookUpService {
  constructor(
    private readonly salesforceService: SalesforceService,
    private readonly dynamodbService: DynamoDBService,
    private readonly oapTranslationService: OapTranslationService,
    @Inject(forwardRef(() => OapService))
    private readonly oapService: OapService,
  ) {}
  async getLevel(APIKEY, request): Promise<any> {
    const allLevels = await this.salesforceService.fetchData(
      'gus/level',
      APIKEY,
      request,
    );
    return allLevels.map((item) => {
      return {
        label: item.Name,
        value: item.Id,
      };
    });
  }
  async getLocations(APIKEY, request, programId = null): Promise<any> {
    const allLevels = await this.salesforceService.fetchData(
      programId
        ? `gus/getlocations/?programId=${programId}`
        : `gus/getlocations`,
      APIKEY,
      request,
    );
    return allLevels.map((item) => {
      return {
        label: item.Location__c,
        value: item.Location__c,
      };
    });
  }
  async getCountry(APIKEY, request, brand?): Promise<any> {
    const countries = await this.salesforceService.fetchData(
      'gus/country',
      APIKEY,
      request,
    );

    const countriesFromSF = countries.map((item: any) => {
      return {
        label: item.Label,
        value: item.Value,
      };
    });

    switch (brand) {
      case 'UEG': {
        const priorityCountries = this.priorityCountriesBasedOnBrands[brand];
        const priorityCountriesList = priorityCountries
          .map((countryName: any) =>
            countriesFromSF.find(
              (country: any) => country.label === countryName,
            ),
          )
          .filter((country: any) => country !== undefined);

        const otherCountries = countriesFromSF.filter(
          (country: any) => !priorityCountries.includes(country.label),
        );

        return [...priorityCountriesList, ...otherCountries];
      }
      default:
        return countriesFromSF;
    }
  }

  async getInstitutionNamesByCountry(country: string): Promise<any> {
    const params = {
      TableName: process.env.OAP_SF_INSTITUTION_CACHE_TABLE_NAME,
      KeyConditionExpression: 'PK = :pkValue',
      ExpressionAttributeValues: {
        ':pkValue': `COUNTRY#${country}`,
      },
    };

    console.log('Params', params);
    try {
      const institutionNames = await this.dynamodbService.queryObjects(params);

      return institutionNames.Items.map((item) => {
        return {
          label: item.Institution_Name__c,
          value: item.Id,
        };
      });
    } catch (error) {
      console.error('Error fetching institution names by country:', error);
      throw new Error('Unable to fetch institution names.');
    }
  }

  async getLanguages(APIKEY, request): Promise<any> {
    const languages = await this.salesforceService.fetchData(
      'gus/languages',
      APIKEY,
      request,
    );
    return languages.map((item) => {
      return {
        label: item.Label,
        value: item.Value,
      };
    });
  }

  async getProgramLanguages(event, APIKEY, request): Promise<any> {
    const languages = await this.salesforceService.fetchData(
      `gus/programlanguages?level='${event.level}'&brand='${event.brand}'`,
      APIKEY,
      request,
    );
    console.log('languages ', languages);
    return languages.map((item) => {
      return {
        label: item.Language__c,
        value: item.Language__c,
      };
    });
  }

  async getProgramByLevelAndLanguage(event, APIKEY, request): Promise<any> {
    const programmes = await this.salesforceService.fetchData(
      `gus/programbylanguage?level='${event.level}'&brand='${event.brand}'&language='${event.language}'`,
      APIKEY,
      request,
    );
    console.log('programmes ', programmes);

    const uniqueProgrammes = new Map();
    programmes.forEach((item) => {
      if (!uniqueProgrammes.has(item.Programme__c)) {
        uniqueProgrammes.set(item.Programme__c, {
          label: item.ProgrammeName__c,
          value: item.Programme__c,
        });
      }
    });

    return Array.from(uniqueProgrammes.values());
  }

  async getProgramLocations(event, APIKEY, request): Promise<any> {
    const locations = await this.salesforceService.fetchData(
      `gus/programlocations?level='${event.level}'&brand='${event.brand}'&language='${event.language}'&program='${event.program}'`,
      APIKEY,
      request,
    );
    console.log('locations ', locations);

    return locations.map((item) => {
      return {
        label: item.Location__c,
        value: item.Location__c,
      };
    });
  }

  async getProgramDuration(event, APIKEY, request): Promise<any> {
    const duration = await this.salesforceService.fetchData(
      `gus/programduration?level='${event.level}'&brand='${event.brand}'&language='${event.language}'&program='${event.program}'&location='${event.location}'`,
      APIKEY,
      request,
    );
    console.log('Raw duration:', duration);

    const uniqueDurations = new Map();

    duration.forEach((item) => {
      if (!uniqueDurations.has(item.Duration__c)) {
        uniqueDurations.set(item.Duration__c, {
          label: Number(item.Duration__c), // Ensure it's a number
          value: Number(item.Duration__c),
        });
      }
    });

    const sortedDurations = Array.from(uniqueDurations.values()).sort(
      (a, b) => a.label - b.label,
    );

    console.log('Sorted durations:', sortedDurations);
    return sortedDurations;
  }

  async getProgramIntake(event, APIKEY, request): Promise<any> {
    const programmeId = event.program;
    const brand = event.brand?.trim().replace(/^'|'$/g, '');
    const email = event.email;
    const currentIntake = event.currentIntake;
    const productId = event.productId;

    const programmeIntakes = await this.salesforceService.fetchData(
      `gus/programintake?level='${event.level}'&brand='${event.brand}'&language='${event.language}'&program='${programmeId}'&location='${event.location}'&duration=${event.duration}`,
      APIKEY,
      request,
    );

    let filteredIntakes = programmeIntakes;

    console.log('Filtered intakes:', filteredIntakes);
    // Filter by current intake if provided
    if (currentIntake) {
      const currentIntakeDate = new Date(currentIntake);
      if (!isNaN(currentIntakeDate.getTime())) {
        filteredIntakes = programmeIntakes.filter((intake) => {
          const intakeDate = new Date(intake.Intake__c);
          return intakeDate > currentIntakeDate;
        });
      }
    } else if (email) {
      const studentApplications =
        await this.oapService.getStudentsOapApplications(email, brand);
      console.log('Student apps:', studentApplications);
      const studentProgramApplications = studentApplications.filter(
        (app) => app.programId === programmeId && app.intake,
      );
      console.log('Student program apps:', studentProgramApplications);
      if (studentProgramApplications.length > 0) {
        const studentIntakeDates = studentProgramApplications
          .map((app) => new Date(app.intake))
          .filter((date) => !isNaN(date.getTime()));

        if (studentIntakeDates.length > 0) {
          const latestStudentIntake = new Date(Math.max(...studentIntakeDates));
          filteredIntakes = programmeIntakes.filter((intake) => {
            const intakeDate = new Date(intake.Intake__c);
            return intakeDate > latestStudentIntake || productId === intake.Id;
          });
        }
      }
    }

    // Return filtered intakes with generic formatting
    return filteredIntakes.map((item) => {
      const dateObj = new Date(item.Intake__c);
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
      };
      const formattedDate: string = dateObj.toLocaleDateString(
        'en-US',
        options,
      );
      return {
        label: formattedDate,
        value: item.Intake__c,
        productId: item.Id,
      };
    });
  }

  async getProgrammes(
    APIKEY,
    request,
    email?,
    brand?,
    byProduct?,
  ): Promise<any> {
    try {
      if (!APIKEY) {
        throw new HttpException('API key is required', HttpStatus.BAD_REQUEST);
      }

      if (!request) {
        throw new HttpException(
          'Request object is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      let studentApplications = [];
      try {
        if (email && brand) {
          studentApplications =
            await this.oapService.getStudentsOapApplications(email, brand);
        }
      } catch (error) {
        console.error('Error fetching student applications:', error);
        throw new HttpException(
          'Failed to fetch student applications',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      // Convert byProduct to boolean explicitly
      const isByProduct = byProduct === 'true' || byProduct === false;
      console.log('byProduct value:', byProduct);
      console.log('isByProduct converted:', isByProduct);

      if (isByProduct) {
        try {
          return await this.getUserProgrammes(
            studentApplications,
            APIKEY,
            request,
          );
        } catch (error) {
          console.error('Error getting user programmes:', error);
          throw new HttpException(
            'Failed to fetch user programmes',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } else {
        try {
          const filledProgrammeIds = new Set(
            studentApplications.map((p) => p.programmeId),
          );

          const programmes = await this.salesforceService.fetchData(
            `gus/programme`,
            APIKEY,
            request,
          );

          if (!programmes || !Array.isArray(programmes)) {
            throw new HttpException(
              'Invalid response from Salesforce',
              HttpStatus.BAD_GATEWAY,
            );
          }

          const notFilledPrograms = programmes.filter(
            (p) => !filledProgrammeIds.has(p.Programme__c),
          );

          return notFilledPrograms
            .map((item) => {
              if (!item.Name || !item.Id) {
                console.warn('Programme missing required fields:', item);
                return null;
              }
              return {
                label: item.Name,
                value: item.Id,
                level: item.Level_Name__c,
                levelId: item.Level__c,
              };
            })
            .filter(Boolean); // Remove null entries
        } catch (error) {
          console.error('Error fetching programmes:', error);
          if (error instanceof HttpException) {
            throw error;
          }
          throw new HttpException(
            'Failed to fetch programmes from Salesforce',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }
    } catch (error) {
      console.error('Error in getProgrammes:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          message: 'An unexpected error occurred while fetching programmes',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async getUserProgrammes(studentApplications, APIKEY, request): Promise<any> {
    const userProgramMap = new Map();
    studentApplications.forEach((p) => {
      const existing = userProgramMap.get(p.programId) || [];
      if (!p.intake || isNaN(new Date(p.intake).getTime())) return;
      existing.push(new Date(p.intake));
      userProgramMap.set(p.programId, existing);
    });
    const payload = {
      object: 'getProducts',
      entityName: 'Product2',
    };

    // Call Salesforce API
    const totalPrograms = await this.salesforceService.postData(
      'gus/sobject',
      payload,
      APIKEY,
      request,
    );
    const futurePrograms = totalPrograms.filter((program) => {
      const programmeId = program.Programme__c;
      const intakeDate = new Date(program.Intake__c);

      if (!userProgramMap.has(programmeId)) return true;

      const userIntakes = userProgramMap.get(programmeId);
      return userIntakes.every((userDate) => intakeDate > userDate);
    });

    // Now extract unique programmeId + programmeName pairs
    const uniqueProgramsMap = new Map();

    futurePrograms.forEach((p) => {
      const key = `${p.Programme__c}:${p.ProgrammeName__c}`;
      if (!uniqueProgramsMap.has(key)) {
        uniqueProgramsMap.set(key, {
          value: p.Programme__c,
          label: p.ProgrammeName__c,
          level: p.Programme__r.Level_Name__c,
          levelId: p.Programme__r.Level__c,
        });
      }
    });

    return Array.from(uniqueProgramsMap.values());
  }
  async getPathwayProviders(APIKEY, request): Promise<any> {
    const pathwayProviders = await this.salesforceService.fetchData(
      `gus/pathwayprovider`,
      APIKEY,
      request,
    );
    return pathwayProviders.map((item) => {
      return {
        label: item.PathwayPartner__c,
        value: item.Id,
      };
    });
  }
  async getProgrammeByLevel(level, APIKEY, request, event): Promise<any> {
    const programmes = await this.salesforceService.fetchData(
      `gus/getprogrammebylevel/${level}`,
      APIKEY,
      request,
    );

    const mappedProgrammes = programmes.map((item: any) => ({
      label: item.Name,
      value: item.Id,
    }));

    if (event?.sortByLabel === 'true') {
      mappedProgrammes.sort((a: any, b: any) => a.label.localeCompare(b.label));
    }

    return mappedProgrammes;
  }
  async getStates(countryCode: string, APIKEY: string, request): Promise<any> {
    const states = await this.salesforceService.fetchData(
      `gus/states/${countryCode}`,
      APIKEY,
      request,
    );
    return states.map((item: any) => {
      return {
        label: item.label,
        value: item.value,
      };
    });
  }
  // old api to get intake with id only
  async getIntakeByProgrammeId(programmeId, APIKEY, request): Promise<any> {
    const programmeIntakes = await this.salesforceService.fetchData(
      `gus/getintakebyprogramme/${programmeId}`,
      APIKEY,
      request,
    );

    return programmeIntakes.map((item) => {
      const dateObj = new Date(item.Intake__c);
      const formattedDate = new Intl.DateTimeFormat('en-US', {
        month: 'long',
        year: 'numeric',
      }).format(dateObj);
      return {
        label: formattedDate,
        value: item.Intake__c,
        productId: item.Id,
        weeks: this.monthsToWeeks(item.Duration__c),
        id: item.Id__c,
      };
    });
  }
  // new api to get intake with id, query
  async getIntakeByProgrammeIdWithFilter(
    programmeId,
    APIKEY,
    params,
    request,
    brand,
    email?,
    productId?,
    currentIntake?,
  ): Promise<any> {
    const programmeIntakes = await this.salesforceService.postData(
      `gus/getprogrammedetailsbyid/${programmeId}`,
      params,
      APIKEY,
      request,
    );

    let filteredIntakes = programmeIntakes;

    // Filter by current intake date if provided
    if (currentIntake) {
      const currentIntakeDate = new Date(currentIntake);
      if (!isNaN(currentIntakeDate.getTime())) {
        filteredIntakes = programmeIntakes.filter((intake) => {
          const intakeDate = new Date(intake.Intake__c);
          return intakeDate > currentIntakeDate;
        });
      }
    } else if (email) {
      const studentApplications =
        await this.oapService.getStudentsOapApplications(email, brand);

      // Get all applications for this specific program
      const studentProgramApplications = studentApplications.filter(
        (app) => app.programId === programmeId && app.intake,
      );

      if (studentProgramApplications.length > 0) {
        // Get all intake dates for this program
        const studentIntakeDates = studentProgramApplications
          .map((app) => new Date(app.intake))
          .filter((date) => !isNaN(date.getTime())); // Filter out invalid dates
        console.log('studentIntakeDates', studentIntakeDates);

        if (studentIntakeDates.length > 0) {
          // Get the latest intake date
          const latestStudentIntake = new Date(Math.max(...studentIntakeDates));

          // Filter intakes that are after the student's latest intake date
          filteredIntakes = programmeIntakes.filter((intake) => {
            const intakeDate = new Date(intake.Intake__c);
            return intakeDate > latestStudentIntake || productId === intake.Id;
          });
        }
      }
    }

    return filteredIntakes.map((item) => {
      const formattedDate = this.oapService.formatDateByBrand(
        item.Intake__c,
        brand,
      );

      const result = {
        label: formattedDate,
        value: item.Intake__c,
        productId: item.Id,
      };

      // Add brand-specific properties
      switch (brand) {
        case 'IBAT':
          return {
            ...result,
            weeks: this.daysToWeeks(item.Campus_Days__c),
          };
        case 'HZU':
          return {
            ...result,
            weeks: this.daysToWeeks(item.Campus_Days__c),
            duration: item.Duration__c,
            semesters: item.Semesters__c,
            totalSemesterCreditHours: item.Total_Semester_Credit_Hours__c,
            graduationDate: item.Graduation_Date__c,
            courseUniqueId: item.Institution_Course_Unique_ID__c,
            enrollmentFee: item.Enrollment_Fee__c,
            learnerResourceFee: item.Learner_Resource_Fee__c,
            feeReduction: item.Fee_reduction__c,
            perSemesterProgramFee: item.Per_semester_program_fee__c,
            tuitionRatePerCredit: item.Tuition_rate_per_credit__c,
            totalProgramCost: item.Total_Program_Cost__c,
          };
        case 'UCW':
          return {
            ...result,
            weeks: this.monthsToWeeks(item.Duration__c),
          };
        default:
          return {
            ...result,
            weeks: this.monthsToWeeks(item.Duration__c),
            id: item.Id__c,
          };
      }
    });
  }
  async getProgrammeDetailsById(
    programmeId: string,
    APIKEY: string,
    field: string,
    request,
    params?: any,
  ): Promise<any> {
    const programmeDetails = await this.salesforceService.postData(
      `gus/getprogrammedetailsbyid/${programmeId}`,
      params,
      APIKEY,
      request,
    );

    console.log('programmeDetails', programmeDetails);
    const seen = new Set();
    const res = programmeDetails
      .filter((ele, i) => {
        const thisItem = ele[field];
        if (seen.has(thisItem)) {
          return false;
        } else {
          seen.add(thisItem);
          return true;
        }
      })
      .map((item) => ({
        label: item[field],
        value: item[field],
      }));
    return res;
  }

  async getStudyModesByProgramme(
    programmeId: string,
    APIKEY: string,
    request,
  ): Promise<any> {
    return this.getProgrammeDetailsById(
      programmeId,
      APIKEY,
      'StudyMode__c',
      request,
    );
  }

  async getCourseOptionsByProgramme(
    programmeId: string,
    APIKEY: string,
    request,
    params: any,
  ): Promise<any> {
    return this.getProgrammeDetailsById(
      programmeId,
      APIKEY,
      'CourseOption__c',
      request,
      params,
    );
  }

  async getDurationByProgramme(
    programmeId: string,
    APIKEY: string,
    request,
    params: any,
    getValueFrom: string,
  ): Promise<any> {
    // getting dynamic value from api query
    let response = await this.getProgrammeDetailsById(
      programmeId,
      APIKEY,
      getValueFrom,
      request,
      params,
    );
    console.log('response ->', response);
    response = response.map((item) => ({
      value: item.value,
      label: this.daysToWeeks(item.label),
    }));
    response.sort((a, b) => a.label - b.label);
    return response;
  }
  monthsToWeeks(months: number): number {
    const weeksInMonth = 4;
    const weeks = months * weeksInMonth;
    return weeks;
  }
  daysToWeeks(days: number): number {
    const daysInWeek = 7;
    const weeks = days / daysInWeek;
    return weeks;
  }

  priorityCountriesBasedOnBrands = {
    UEG: [
      'Germany',
      'Austria',
      'Switzerland',
      'India',
      'China',
      'Ghana',
      'Nigeria',
      'Spain',
    ],
  };

  async getTranslatedPicklistValues(
    lang: string,
    key: string,
    brand?: string,
  ): Promise<any> {
    if (!lang || !key) {
      throw new Error('Language and key are required');
    }

    const tableName = `gus-eip-picklist-${process.env.STAGE}`;
    const sk = brand ? `${key}#${brand}` : key;

    try {
      const result = await this.dynamodbService.getObject(tableName, {
        PK: lang,
        SK: sk,
      });

      if (!result.Item) {
        return null;
      }

      return result.Item;
    } catch (error) {
      console.error('Error fetching translation:', error);
      throw error;
    }
  }

  async translateAndMergePicklist(
    lang: string,
    key: string,
    existingPicklist: any[],
  ): Promise<any[]> {
    if (!lang || !key || !Array.isArray(existingPicklist)) {
      throw new Error('Language, key and existing picklist are required');
    }

    try {
      const translatedPicklist = await this.getTranslatedPicklistValues(
        lang,
        key,
      );
      console.log('Get translated picklist from DynamoDB', translatedPicklist);

      let updatedTranslations = [];
      let needsUpdate = false;

      if (!translatedPicklist) {
        updatedTranslations = [];
        needsUpdate = true;
      } else {
        updatedTranslations = [...translatedPicklist.data];
      }

      for (const item of existingPicklist) {
        const existingTranslation = updatedTranslations.find(
          (t) => t.label === item.label,
        );

        if (!existingTranslation) {
          const translatedText = await this.oapTranslationService.translateText(
            item.label,
            'en',
            lang,
          );
          updatedTranslations.push({
            ...item, // Preserve all additional fields from the original item
            label: item.label,
            displayText: translatedText,
            value: item.value,
          });
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        const updatedPicklist = {
          PK: lang,
          SK: key,
          data: updatedTranslations,
          updatedAt: new Date().toISOString(),
        };

        await this.dynamodbService.putObject(
          `gus-eip-picklist-${process.env.STAGE}`,
          { Item: updatedPicklist },
        );
      }
      // Return only the existing picklist items with their translations
      const existingPicklistLabels = new Set(
        existingPicklist.map((item) => item.label),
      );
      return updatedTranslations.filter((item) =>
        existingPicklistLabels.has(item.label),
      );
    } catch (error) {
      console.error('Error in translateAndMergePicklist:', error);
      throw error;
    }
  }

  async getBrandFromApiKey(apiKey: string): Promise<string> {
    if (!apiKey) {
      throw new Error('API key is required');
    }

    try {
      const tableName = `gus-eip-consumer-${process.env.STAGE}`;
      const result = await this.dynamodbService.getObject(tableName, {
        PK: apiKey,
      });

      if (!result.Item) {
        throw new Error('Brand not found for the given API key');
      }

      return result.Item.brand;
    } catch (error) {
      console.error('Error fetching brand from API key:', error);
      throw error;
    }
  }

  async getProgramDelivery(
    event: any,
    APIKEY: string,
    request: any,
  ): Promise<any> {
    // Define the mapping of query params to Salesforce fields
    const fieldMapping = {
      program: 'Programme__c',
      location: 'Location__c',
    };

    // Build customConditions object from query params
    const customConditions = {};
    for (const [queryParam, sfField] of Object.entries(fieldMapping)) {
      if (event[queryParam]) {
        customConditions[sfField] = event[queryParam];
      }
    }

    // Prepare request payload
    const payload = {
      object: 'programDeliveryDetails',
      customConditions: customConditions,
      entityName: 'Product2',
    };

    // Call Salesforce API
    const response = await this.salesforceService.postData(
      'gus/sobject',
      payload,
      APIKEY,
      request,
    );

    // Create a Map to store unique values
    const uniqueDeliveries = new Map();

    response.forEach((item) => {
      if (!uniqueDeliveries.has(item.DeliveryName__c)) {
        uniqueDeliveries.set(item.DeliveryName__c, {
          label: item.DeliveryName__c,
          value: item.DeliveryName__c,
        });
      }
    });

    // Convert Map to array and sort by label
    const sortedResponse = Array.from(uniqueDeliveries.values()).sort((a, b) =>
      a.label.localeCompare(b.label),
    );

    return sortedResponse;
  }

  // Unified OAP Product API
  async getOapProduct(
    type: string,
    filters: any,
    APIKEY: string,
    request: any,
    email?: string,
    brand?: string,
    currentProductId?: string,
  ): Promise<any> {
    let object: string;
    let groupBy: string | undefined;
    let responseMapper: (items: any[]) => any[];

    switch (type) {
      case 'location':
        object = 'oapLocations';
        groupBy = 'Location__c';
        responseMapper = (items) => {
          const mapped = items
            .filter((item: any) => item.Location__c != null)
            .map((item: any) => ({
              label: item.Location__c,
              value: item.Location__c,
            }));
          return mapped.sort((a, b) => {
            if (!a.label) return 1;
            if (!b.label) return -1;
            return a.label.localeCompare(b.label);
          });
        };
        break;

      case 'level':
        object = 'oapLevels';
        responseMapper = (items) => {
          const uniqueLevels = new Map();
          items
            .filter((item: any) => item.Programme__r?.Level__c != null && item.ProgrammeLevel__c != null)
            .forEach((item: any) => {
              if (!uniqueLevels.has(item.Programme__r.Level__c)) {
                uniqueLevels.set(item.Programme__r.Level__c, {
                  label: item.ProgrammeLevel__c,
                  value: item.Programme__r.Level__c,
                });
              }
            });
          const result = Array.from(uniqueLevels.values());
          return result.sort((a, b) => {
            if (!a.label) return 1;
            if (!b.label) return -1;
            return a.label.localeCompare(b.label);
          });
        };
        break;

      case 'language':
        object = 'oapLanguages';
        groupBy = 'Language__c';
        responseMapper = (items) => {
          console.log('items ->',items)
          const mapped = items
            .filter((item: any) => item.Language__c != null)
            .map((item: any) => ({
              label: item.Language__c,
              value: item.Language__c,
            }));
          return mapped.sort((a, b) => {
            if (!a.label) return 1;
            if (!b.label) return -1;
            return a.label.localeCompare(b.label);
          });
        };
        break;

      case 'programme':
        object = 'oapPrograms';
        responseMapper = (items) => {
          const uniquePrograms = new Map();
          items
            .filter((item: any) => item.Programme__c != null && item.ProgrammeName__c != null)
            .forEach((item: any) => {
              if (!uniquePrograms.has(item.Programme__c)) {
                uniquePrograms.set(item.Programme__c, {
                  label: item.ProgrammeName__c,
                  value: item.Programme__c,
                });
              }
            });
          const result = Array.from(uniquePrograms.values());
          return result.sort((a, b) => {
            if (!a.label) return 1;
            if (!b.label) return -1;
            return a.label.localeCompare(b.label);
          });
        };
        break;

      case 'duration':
        object = 'oapDurations';
        responseMapper = (items) => {
          const uniqueDurations = new Map();
          items
            .filter((item: any) => item.Duration__c != null)
            .forEach((item: any) => {
              if (!uniqueDurations.has(item.Duration__c)) {
                uniqueDurations.set(item.Duration__c, {
                  label: Number(item.Duration__c),
                  value: item.Duration__c,
                });
              }
            });
          const result = Array.from(uniqueDurations.values());
          return result.sort((a, b) => {
            if (typeof a.label !== 'number') return 1;
            if (typeof b.label !== 'number') return -1;
            return a.label - b.label;
          });
        };
        break;

      case 'intake':
        object = 'oapIntakes';
        responseMapper = (items) => {
          const result = items
            .filter((item: any) => item.Intake__c != null && item.Id != null)
            .map((item: any) => ({
                label: this.oapService.formatDateByBrand(item.Intake__c, brand || 'default'),
                value: item.Intake__c,
                productId: item.Id,
            }));
          return result.sort((a, b) => {
            if (!a.value) return 1;
            if (!b.value) return -1;
            return new Date(a.value).getTime() - new Date(b.value).getTime();
          });
        };
        break;

      case 'delivery':
        object = 'oapDeliveries';
        responseMapper = (items) => {
          const uniqueDeliveries = new Map();
          items.forEach((item: any) => {
            if (!uniqueDeliveries.has(item.DeliveryName__c)) {
              uniqueDeliveries.set(item.DeliveryName__c, {
                label: item.DeliveryName__c,
                value: item.DeliveryName__c,
              });
            }
          });
          const result = Array.from(uniqueDeliveries.values());
          return result.sort((a, b) => a.label.localeCompare(b.label));
        };
        break;

      default:
        throw new Error(`Invalid type: ${type}. Supported types: location, level, language, programme, duration, intake, delivery`);
    }

    // Filters are passed as customConditions and will be handled by middleware
    const payload = {
      object,
      customConditions: filters,
      entityName: 'Product2',
      ...(groupBy && { groupBy }),
    };

    const response = await this.salesforceService.postData(
      'gus/sobject',
      payload,
      APIKEY,
      request,
    );

    // Handle email filtering for intakes and programmes
    let filteredResponse = response;
    if (email && (type === 'intake' || type === 'programme')) {
      try {
        // Brand is already fetched in controller
        const studentApplications = await this.oapService.getStudentsOapApplications(email, brand);
        console.log('Student applications:', studentApplications);

        // All brands use standard field names: 'intake' and 'programId'

        if (type === 'intake' && filters.Programme__c) {
          // For intake queries with programme filter: filter by programme and show future intakes
          const studentProgramApplications = studentApplications.filter(
            (app: any) => app.programId === filters.Programme__c && app.intake,
          );

          if (studentProgramApplications.length > 0) {
            const studentIntakeDates = studentProgramApplications
              .map((app: any) => new Date(app.intake))
              .filter((date: any) => !isNaN(date.getTime()));

            if (studentIntakeDates.length > 0) {
              const latestStudentIntake = new Date(Math.max(...studentIntakeDates));
              console.log('Latest student intake:', latestStudentIntake);
              filteredResponse = response.filter((item: any) => {
                const intakeDate = new Date(item.Intake__c);
                return intakeDate > latestStudentIntake || (currentProductId && item.Id === currentProductId);
              });
            }
          }
        } else if (type === 'programme') {
          if (!filters.Programme__c) {
            // For programme queries without programme filter: check each programme for future intakes
            const programmeIntakeMap = new Map();

            // Group response by programme
            response.forEach((item: any) => {
              const programmeId = item.Programme__c;
              if (!programmeIntakeMap.has(programmeId)) {
                programmeIntakeMap.set(programmeId, {
                  programme: item,
                  intakes: []
                });
              }
              programmeIntakeMap.get(programmeId).intakes.push(new Date(item.Intake__c));
            });

            // Filter programmes that have future intakes
            const validProgrammes = [];
            for (const [programmeId, data] of programmeIntakeMap) {
              // Check if student has any application for this programme
              const allStudentProgramApplications = studentApplications.filter(
                (app: any) => app.programId === programmeId,
              );

              if (allStudentProgramApplications.length === 0) {
                // No previous applications for this programme, include it
                validProgrammes.push(data.programme);
              } else {
                // Check if student has application without intake selected
                const applicationsWithoutIntake = allStudentProgramApplications.filter(
                  (app: any) => !app.intake,
                );

                if (applicationsWithoutIntake.length > 0) {
                  // Student has application for this programme without intake selected
                  // Filter out this programme to prevent duplicate applications
                  continue;
                }

                // All applications have intakes, check if programme has future intakes
                const studentProgramApplications = allStudentProgramApplications.filter(
                  (app: any) => app.intake,
                );
                const studentIntakeDates = studentProgramApplications
                  .map((app: any) => new Date(app.intake))
                  .filter((date: any) => !isNaN(date.getTime()));

                if (studentIntakeDates.length > 0) {
                  const latestStudentIntake = new Date(Math.max(...studentIntakeDates));
                  const hasFutureIntakes = data.intakes.some((intakeDate: Date) => intakeDate > latestStudentIntake);

                  if (hasFutureIntakes) {
                    validProgrammes.push(data.programme);
                  }
                }
              }
            }

            filteredResponse = validProgrammes;
          } else {
            // For programme queries with programme filter: check if this specific programme has future intakes
            const programmeId = filters.Programme__c;
            const allStudentProgramApplications = studentApplications.filter(
              (app: any) => app.programId === programmeId,
            );

            if (allStudentProgramApplications.length > 0) {
              // Check if student has application without intake selected
              const applicationsWithoutIntake = allStudentProgramApplications.filter(
                (app: any) => !app.intake,
              );

              if (applicationsWithoutIntake.length > 0) {
                // Student has application for this programme without intake selected
                // Filter out this programme to prevent duplicate applications
                filteredResponse = [];
              } else {
                // All applications have intakes, check if programme has future intakes
                const studentProgramApplications = allStudentProgramApplications.filter(
                  (app: any) => app.intake,
                );
                const studentIntakeDates = studentProgramApplications
                  .map((app: any) => new Date(app.intake))
                  .filter((date: any) => !isNaN(date.getTime()));

                if (studentIntakeDates.length > 0) {
                  const latestStudentIntake = new Date(Math.max(...studentIntakeDates));

                  // Get all intakes for this programme to check for future ones
                  const programmeIntakes = response.map((item: any) => new Date(item.Intake__c));
                  const hasFutureIntakes = programmeIntakes.some((intakeDate: Date) => intakeDate > latestStudentIntake);

                  if (!hasFutureIntakes) {
                    // No future intakes available for this programme
                    filteredResponse = [];
                  }
                }
              }
            }
            // If no previous applications, keep all programmes (no filtering needed)
          }
        }
      } catch (error) {
        console.log('Error filtering by email:', error);
        // Continue with unfiltered response if there's an error
      }
    }

    return responseMapper(filteredResponse);
  }
}

import { Injectable } from '@nestjs/common';
import * as XLSX from 'xlsx';
import * as csv from 'csv-parser';
import { Readable } from 'stream';

export interface ParsedUserData {
  email: string;
  phone_number?: string;
  name?: string;
  country?: string;
  dob?: string;
  [key: string]: any;
}

@Injectable()
export class FileParserService {
  /**
   * Parse XLSX file buffer and map columns according to configuration
   */
  async parseXlsxFile(
    fileBuffer: Buffer,
    columnMappings: Record<string, string>,
  ): Promise<ParsedUserData[]> {
    try {
      console.log('Column mappings:', columnMappings);
      // Read the workbook from buffer
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      
      // Get the first worksheet
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // Convert to JSON
      const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      console.log('Raw data:', rawData);
      
      if (rawData.length === 0) {
        throw new Error('Excel file is empty');
      }
      
      // Get headers from first row
      const headers = rawData[0] as string[];
      const dataRows = rawData.slice(1) as any[][];
      
      // Map and transform data
      const mappedData: ParsedUserData[] = [];
      
      for (const row of dataRows) {
        if (this.isEmptyRow(row)) continue;
        
        const mappedRow: ParsedUserData = { email: '' };
        
        // Map columns according to configuration
        headers.forEach((header, index) => {
          const mappedField = columnMappings[header];
          if (mappedField && row[index] !== undefined && row[index] !== null) {
            mappedRow[mappedField] = this.cleanValue(row[index]);
          }
        });
        
        // Validate that email is present
        if (mappedRow.email) {
          mappedData.push(mappedRow);
        }
      }
      
      return mappedData;
    } catch (error) {
      throw new Error(`Failed to parse XLSX file: ${error.message}`);
    }
  }

  /**
   * Parse CSV file buffer and map columns according to configuration
   */
  async parseCsvFile(
    fileBuffer: Buffer,
    columnMappings: Record<string, string>,
  ): Promise<ParsedUserData[]> {
    return new Promise((resolve, reject) => {
      const results: ParsedUserData[] = [];
      const stream = Readable.from(fileBuffer);
      
      stream
        .pipe(csv())
        .on('data', (row) => {
          try {
            const mappedRow: ParsedUserData = { email: '' };
            
            // Map columns according to configuration
            Object.keys(row).forEach((header) => {
              const mappedField = columnMappings[header];
              if (mappedField && row[header] !== undefined && row[header] !== null) {
                mappedRow[mappedField] = this.cleanValue(row[header]);
              }
            });
            
            // Validate that email is present
            if (mappedRow.email) {
              results.push(mappedRow);
            }
          } catch (error) {
            console.error('Error processing CSV row:', error);
          }
        })
        .on('end', () => {
          resolve(results);
        })
        .on('error', (error) => {
          reject(new Error(`Failed to parse CSV file: ${error.message}`));
        });
    });
  }

  /**
   * Parse file based on file type
   */
  async parseFile(
    fileBuffer: Buffer,
    fileType: 'xlsx' | 'csv',
    columnMappings: Record<string, string>,
  ): Promise<ParsedUserData[]> {
    switch (fileType) {
      case 'xlsx':
        return this.parseXlsxFile(fileBuffer, columnMappings);
      case 'csv':
        return this.parseCsvFile(fileBuffer, columnMappings);
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  }

  /**
   * Check if a row is empty
   */
  private isEmptyRow(row: any[]): boolean {
    return row.every(cell => cell === undefined || cell === null || cell === '');
  }

  /**
   * Clean and normalize cell values
   */
  private cleanValue(value: any): string {
    if (value === undefined || value === null) {
      return '';
    }
    
    return String(value).trim();
  }

  /**
   * Validate parsed user data
   */
  validateUserData(userData: ParsedUserData[]): { valid: ParsedUserData[], invalid: any[] } {
    const valid: ParsedUserData[] = [];
    const invalid: any[] = [];
    
    userData.forEach((user, index) => {
      const errors: string[] = [];
      
      // Validate email
      if (!user.email) {
        errors.push('Email is required');
      } else if (!this.isValidEmail(user.email)) {
        errors.push('Invalid email format');
      }
      
      // Validate phone number format if present
      // if (user.phone_number && !this.isValidPhoneNumber(user.phone_number)) {
      //   errors.push('Invalid phone number format');
      // }
      
      if (errors.length === 0) {
        valid.push(user);
      } else {
        invalid.push({
          rowIndex: index + 1,
          data: user,
          errors,
        });
      }
    });
    
    return { valid, invalid };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format (basic validation)
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    // Check if it's between 10-15 digits
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  }
}

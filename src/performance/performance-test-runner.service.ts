import { Injectable } from '@nestjs/common';
import { PerformanceMonitorService, PerformanceMetrics } from './performance-monitor.service';
import { OapService } from '../oap/service';
import { UserMigrationRequestDto } from '../oap/dto/legacy-user.dto';

export interface TestConfiguration {
  name: string;
  description: string;
  batchSize: number;
  testDataSize: number;
  iterations: number;
  oapName: string;
  mode: string;
}

export interface TestResult {
  configuration: TestConfiguration;
  metrics: PerformanceMetrics;
  timestamp: string;
  success: boolean;
  errorMessage?: string;
}

export interface TimelineEstimate {
  conservative: {
    hours: number;
    description: string;
    confidence: string;
  };
  realistic: {
    hours: number;
    description: string;
    confidence: string;
  };
  optimistic: {
    hours: number;
    description: string;
    confidence: string;
  };
  assumptions: string[];
  risks: string[];
}

@Injectable()
export class PerformanceTestRunnerService {
  private testResults: TestResult[] = [];

  constructor(
    private performanceMonitor: PerformanceMonitorService,
    private oapService: OapService,
  ) {}

  async runPerformanceTestSuite(apiKey: string): Promise<TestResult[]> {
    console.log('🚀 Starting Performance Test Suite for /migratelegacyusers API');
    
    const testConfigurations: TestConfiguration[] = [
      {
        name: 'Baseline Performance Test',
        description: 'Default configuration with full dataset',
        batchSize: 15,
        testDataSize: 800,
        iterations: 1,
        oapName: 'performance-test',
        mode: 'STUDENT',
      },
      {
        name: 'Small Batch Optimization',
        description: 'Testing with smaller batch sizes',
        batchSize: 5,
        testDataSize: 400,
        iterations: 2,
        oapName: 'performance-test',
        mode: 'STUDENT',
      },
      {
        name: 'Medium Batch Optimization',
        description: 'Testing with medium batch sizes',
        batchSize: 10,
        testDataSize: 400,
        iterations: 2,
        oapName: 'performance-test',
        mode: 'STUDENT',
      },
      {
        name: 'Large Batch Optimization',
        description: 'Testing with larger batch sizes',
        batchSize: 20,
        testDataSize: 400,
        iterations: 2,
        oapName: 'performance-test',
        mode: 'STUDENT',
      },
      {
        name: 'Maximum Batch Test',
        description: 'Testing with maximum allowed batch size',
        batchSize: 25,
        testDataSize: 400,
        iterations: 2,
        oapName: 'performance-test',
        mode: 'STUDENT',
      },
    ];

    for (const config of testConfigurations) {
      console.log(`\n📊 Running Test: ${config.name}`);
      console.log(`   Description: ${config.description}`);
      console.log(`   Batch Size: ${config.batchSize}, Data Size: ${config.testDataSize}`);
      
      for (let iteration = 1; iteration <= config.iterations; iteration++) {
        console.log(`   Iteration ${iteration}/${config.iterations}`);
        
        try {
          const result = await this.runSingleTest(config, apiKey, iteration);
          this.testResults.push(result);
          
          console.log(`   ✅ Completed in ${(result.metrics.totalProcessingTime / 1000).toFixed(2)}s`);
          console.log(`   📈 Throughput: ${result.metrics.throughputMetrics.usersPerMinute.toFixed(2)} users/min`);
          
          // Add delay between tests to avoid overwhelming the system
          if (iteration < config.iterations) {
            console.log('   ⏳ Waiting 30 seconds before next iteration...');
            await this.delay(30000);
          }
        } catch (error) {
          console.error(`   ❌ Test failed: ${error.message}`);
          this.testResults.push({
            configuration: config,
            metrics: null,
            timestamp: new Date().toISOString(),
            success: false,
            errorMessage: error.message,
          });
        }
      }
      
      // Longer delay between different test configurations
      console.log('⏳ Waiting 60 seconds before next test configuration...');
      await this.delay(60000);
    }

    console.log('\n🎉 Performance Test Suite Completed!');
    return this.testResults;
  }

  private async runSingleTest(
    config: TestConfiguration,
    apiKey: string,
    iteration: number,
  ): Promise<TestResult> {
    // Reset performance monitor for clean metrics
    this.performanceMonitor.reset();
    
    const migrationRequest: UserMigrationRequestDto = {
      oapName: config.oapName,
      mode: config.mode,
    };

    try {
      // Start monitoring
      this.performanceMonitor.startMigration();
      
      // Execute the migration with specified batch size
      const result = await this.oapService.processUserMigration(
        config.oapName,
        config.mode,
        config.batchSize,
      );
      
      // End monitoring
      this.performanceMonitor.endMigration(result.totalUsers);
      
      const metrics = this.performanceMonitor.getMetrics();
      
      return {
        configuration: { ...config, name: `${config.name} (Iteration ${iteration})` },
        metrics,
        timestamp: new Date().toISOString(),
        success: true,
      };
    } catch (error) {
      throw new Error(`Migration test failed: ${error.message}`);
    }
  }

  generateComprehensiveReport(): string {
    if (this.testResults.length === 0) {
      return 'No test results available. Please run the performance test suite first.';
    }

    const successfulTests = this.testResults.filter(r => r.success);
    const failedTests = this.testResults.filter(r => !r.success);

    let report = `
=== COMPREHENSIVE PERFORMANCE TEST REPORT ===
Generated: ${new Date().toISOString()}

EXECUTIVE SUMMARY:
- Total Tests Executed: ${this.testResults.length}
- Successful Tests: ${successfulTests.length}
- Failed Tests: ${failedTests.length}
- Success Rate: ${((successfulTests.length / this.testResults.length) * 100).toFixed(2)}%

`;

    if (successfulTests.length > 0) {
      const bestPerformance = this.findBestPerformance(successfulTests);
      const averagePerformance = this.calculateAveragePerformance(successfulTests);
      
      report += `
PERFORMANCE HIGHLIGHTS:
- Best Throughput: ${bestPerformance.metrics.throughputMetrics.usersPerMinute.toFixed(2)} users/min (${bestPerformance.configuration.name})
- Average Throughput: ${averagePerformance.usersPerMinute.toFixed(2)} users/min
- Best Batch Size: ${bestPerformance.configuration.batchSize}
- Average Processing Time: ${averagePerformance.averageTimePerUser.toFixed(2)} ms per user

`;

      // Detailed results for each test
      report += 'DETAILED TEST RESULTS:\n';
      successfulTests.forEach((result, index) => {
        report += `
${index + 1}. ${result.configuration.name}
   - Batch Size: ${result.configuration.batchSize}
   - Total Time: ${(result.metrics.totalProcessingTime / 1000).toFixed(2)}s
   - Throughput: ${result.metrics.throughputMetrics.usersPerMinute.toFixed(2)} users/min
   - Success Rate: ${result.metrics.batchMetrics.batchSuccessRate.toFixed(2)}%
   - Memory Peak: ${result.metrics.memoryUsage.peak.toFixed(2)} MB
   - Errors: C:${result.metrics.errorCounts.cognitoErrors} D:${result.metrics.errorCounts.databaseErrors} V:${result.metrics.errorCounts.validationErrors}
`;
      });
    }

    if (failedTests.length > 0) {
      report += '\nFAILED TESTS:\n';
      failedTests.forEach((result, index) => {
        report += `${index + 1}. ${result.configuration.name}: ${result.errorMessage}\n`;
      });
    }

    return report;
  }

  calculateTimelineFor85000Users(): TimelineEstimate {
    const successfulTests = this.testResults.filter(r => r.success);
    
    if (successfulTests.length === 0) {
      throw new Error('No successful test results available for timeline calculation');
    }

    const bestPerformance = this.findBestPerformance(successfulTests);
    const averagePerformance = this.calculateAveragePerformance(successfulTests);
    const worstPerformance = this.findWorstPerformance(successfulTests);

    const targetUsers = 85000;

    // Calculate estimates based on different scenarios
    const optimisticMinutes = targetUsers / bestPerformance.metrics.throughputMetrics.usersPerMinute;
    const realisticMinutes = targetUsers / averagePerformance.usersPerMinute;
    const conservativeMinutes = targetUsers / worstPerformance.metrics.throughputMetrics.usersPerMinute;

    // Add buffer for real-world factors
    const optimisticHours = (optimisticMinutes / 60) * 1.2; // 20% buffer
    const realisticHours = (realisticMinutes / 60) * 1.5;   // 50% buffer
    const conservativeHours = (conservativeMinutes / 60) * 2.0; // 100% buffer

    return {
      conservative: {
        hours: Math.ceil(conservativeHours),
        description: `Based on worst-case performance with 100% safety buffer`,
        confidence: '95%',
      },
      realistic: {
        hours: Math.ceil(realisticHours),
        description: `Based on average performance with 50% safety buffer`,
        confidence: '80%',
      },
      optimistic: {
        hours: Math.ceil(optimisticHours),
        description: `Based on best performance with 20% safety buffer`,
        confidence: '60%',
      },
      assumptions: [
        'AWS services maintain current performance levels',
        'No significant rate limiting or throttling occurs',
        'Test data is representative of production data quality',
        'System resources remain available throughout migration',
        'No major errors or data quality issues in production dataset',
        'Network connectivity remains stable',
      ],
      risks: [
        'AWS Cognito rate limiting may reduce throughput',
        'DynamoDB throttling could cause delays',
        'Large dataset may reveal memory or performance issues not seen in testing',
        'Production data quality may be worse than test data',
        'Concurrent system usage may impact performance',
        'Network latency variations in production environment',
      ],
    };
  }

  private findBestPerformance(results: TestResult[]): TestResult {
    return results.reduce((best, current) => 
      current.metrics.throughputMetrics.usersPerMinute > best.metrics.throughputMetrics.usersPerMinute 
        ? current : best
    );
  }

  private findWorstPerformance(results: TestResult[]): TestResult {
    return results.reduce((worst, current) => 
      current.metrics.throughputMetrics.usersPerMinute < worst.metrics.throughputMetrics.usersPerMinute 
        ? current : worst
    );
  }

  private calculateAveragePerformance(results: TestResult[]) {
    const totalThroughput = results.reduce((sum, r) => sum + r.metrics.throughputMetrics.usersPerMinute, 0);
    const totalTimePerUser = results.reduce((sum, r) => sum + r.metrics.throughputMetrics.averageTimePerUser, 0);
    
    return {
      usersPerMinute: totalThroughput / results.length,
      averageTimePerUser: totalTimePerUser / results.length,
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getTestResults(): TestResult[] {
    return [...this.testResults];
  }

  clearResults(): void {
    this.testResults = [];
  }
}
